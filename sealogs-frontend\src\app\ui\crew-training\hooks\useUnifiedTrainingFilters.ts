import { useCallback, useState, useMemo } from 'react'
import { UnifiedTrainingData } from '../utils/crew-training-utils'

export interface UnifiedSearchFilter {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

/**
 * Hook for filtering unified training data on the client side
 * Works with merged training data from mergeAndSortCrewTrainingData
 */
export function useUnifiedTrainingFilters(opts: {
    initialFilter: UnifiedSearchFilter
    unifiedData: UnifiedTrainingData[]
}) {
    const { initialFilter, unifiedData } = opts
    const [filter, setFilter] = useState<UnifiedSearchFilter>(initialFilter)

    const handleFilterChange = useCallback(
        ({ type, data }: { type: string; data: any }) => {
            const next: UnifiedSearchFilter = { ...filter }

            /* ---- vessel ------------------------------------------------------- */
            if (type === 'vessel') {
                if (Array.isArray(data) && data.length) {
                    next.vesselID = { in: data.map((d) => +d.value) }
                } else if (data && !Array.isArray(data)) {
                    next.vesselID = { eq: +data.value }
                } else {
                    delete next.vesselID
                }
            }

            /* ---- trainingType ------------------------------------------------- */
            if (type === 'trainingType') {
                if (Array.isArray(data) && data.length) {
                    next.trainingTypes = { id: { in: data.map((d) => +d.value) } }
                } else if (data && !Array.isArray(data)) {
                    next.trainingTypes = { id: { contains: +data.value } }
                } else {
                    delete next.trainingTypes
                }
            }

            /* ---- trainer ------------------------------------------------------ */
            if (type === 'trainer') {
                if (Array.isArray(data) && data.length) {
                    next.trainer = { id: { in: data.map((d) => +d.value) } }
                } else if (data && !Array.isArray(data)) {
                    next.trainer = { id: { eq: +data.value } }
                } else {
                    delete next.trainer
                }
            }

            /* ---- member ------------------------------------------------------- */
            if (type === 'member') {
                if (Array.isArray(data) && data.length) {
                    next.members = { id: { in: data.map((d) => +d.value) } }
                } else if (data && !Array.isArray(data)) {
                    next.members = { id: { eq: +data.value } }
                } else {
                    delete next.members
                }
            }

            /* ---- dateRange ---------------------------------------------------- */
            if (type === 'dateRange') {
                if (data?.startDate && data?.endDate) {
                    next.date = { gte: data.startDate, lte: data.endDate }
                } else {
                    delete next.date
                }
            }

            /* ---- category ----------------------------------------------------- */
            if (type === 'category') {
                if (data && data !== 'all') {
                    next.category = data
                } else {
                    delete next.category
                }
            }

            setFilter(next)
        },
        [filter],
    )

    // Client-side filtering of unified data
    const filteredData = useMemo(() => {
        if (!unifiedData || !Array.isArray(unifiedData)) {
            console.log('🔍 [useUnifiedTrainingFilters] No unified data provided for filtering')
            return []
        }

        console.log('🔍 [useUnifiedTrainingFilters] Filtering unified data:', {
            totalRecords: unifiedData.length,
            activeFilters: Object.keys(filter).filter(key => filter[key as keyof UnifiedSearchFilter] !== undefined),
            filterValues: filter
        })

        const filtered = unifiedData.filter((item: UnifiedTrainingData) => {
            // Category filter
            if (filter.category && filter.category !== 'all') {
                if (item.category !== filter.category) {
                    return false
                }
            }

            // Vessel filter
            if (filter.vesselID) {
                if (filter.vesselID.eq && item.vesselID !== filter.vesselID.eq) {
                    return false
                }
                if (filter.vesselID.in && !filter.vesselID.in.includes(item.vesselID)) {
                    return false
                }
            }

            // Training type filter
            if (filter.trainingTypes) {
                const trainingTypeId = item.trainingTypeID || item.trainingType?.id
                if (filter.trainingTypes.id?.contains && trainingTypeId !== filter.trainingTypes.id.contains) {
                    return false
                }
                if (filter.trainingTypes.id?.in && !filter.trainingTypes.id.in.includes(trainingTypeId)) {
                    return false
                }
            }

            // Trainer filter (for completed training sessions)
            if (filter.trainer && item.originalData) {
                const trainerId = item.originalData.trainerID || item.originalData.trainer?.id
                if (trainerId) {
                    if (filter.trainer.id?.eq && trainerId !== filter.trainer.id.eq) {
                        return false
                    }
                    if (filter.trainer.id?.in && !filter.trainer.id.in.includes(trainerId)) {
                        return false
                    }
                } else if (filter.trainer.id) {
                    // If trainer filter is applied but no trainer data exists, exclude this item
                    return false
                }
            }

            // Member filter
            if (filter.members && item.members) {
                const memberIds = item.members.map((member: any) => member.id)
                if (filter.members.id?.eq && !memberIds.includes(filter.members.id.eq)) {
                    return false
                }
                if (filter.members.id?.in) {
                    const hasMatchingMember = filter.members.id.in.some(id => memberIds.includes(id))
                    if (!hasMatchingMember) {
                        return false
                    }
                }
            }

            // Date filter
            if (filter.date && item.dueDate) {
                const itemDate = new Date(item.dueDate)
                if (filter.date.gte && itemDate < filter.date.gte) {
                    return false
                }
                if (filter.date.lte && itemDate > filter.date.lte) {
                    return false
                }
            }

            return true
        })

        console.log('🔍 [useUnifiedTrainingFilters] Filtering complete:', {
            originalCount: unifiedData.length,
            filteredCount: filtered.length,
            categoryBreakdown: filtered.reduce((acc: any, item: any) => {
                acc[item.category] = (acc[item.category] || 0) + 1
                return acc
            }, {})
        })

        return filtered
    }, [unifiedData, filter])

    return {
        filter,
        setFilter,
        handleFilterChange,
        filteredData
    }
}
